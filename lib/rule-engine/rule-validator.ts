/**
 * Rule Validator
 * 
 * Validates rule configurations for syntax, semantics, and performance
 */

import { z } from 'zod'
import type {
  RuleValidationResult,
  RuleConfiguration,
  ReferrerRule,
  LocationRule,
  DeviceRule,
  TimeRule,
  ScheduleRule,
  EnhancedLinkCondition,
  ConditionalLinkWithConditions,
} from './types'
import {
  VALIDATION_CONFIG,
  VALIDATION_PATTERNS,
  CONDITION_TYPES,
  PERFORMANCE_THRESHOLDS,
} from './constants'

/**
 * Zod schemas for rule validation
 */
const ReferrerRuleSchema = z.object({
  type: z.literal('referrer'),
  domains: z.array(z.string().regex(VALIDATION_PATTERNS.DOMAIN))
    .min(1, 'At least one domain is required')
    .max(VALIDATION_CONFIG.MAX_DOMAINS_PER_RULE, `Maximum ${VALIDATION_CONFIG.MAX_DOMAINS_PER_RULE} domains allowed`),
  matchType: z.enum(['exact', 'contains', 'regex']),
  caseSensitive: z.boolean(),
  excludeDomains: z.array(z.string().regex(VALIDATION_PATTERNS.DOMAIN)).optional(),
})

const LocationRuleSchema = z.object({
  type: z.literal('location'),
  countries: z.array(z.string().regex(VALIDATION_PATTERNS.COUNTRY_CODE))
    .max(VALIDATION_CONFIG.MAX_COUNTRIES_PER_RULE, `Maximum ${VALIDATION_CONFIG.MAX_COUNTRIES_PER_RULE} countries allowed`)
    .optional(),
  regions: z.array(z.string()).optional(),
  cities: z.array(z.string()).optional(),
  timezones: z.array(z.string().regex(VALIDATION_PATTERNS.TIMEZONE)).optional(),
  excludeCountries: z.array(z.string().regex(VALIDATION_PATTERNS.COUNTRY_CODE)).optional(),
  excludeRegions: z.array(z.string()).optional(),
  excludeCities: z.array(z.string()).optional(),
})

const DeviceRuleSchema = z.object({
  type: z.literal('device'),
  deviceTypes: z.array(z.enum(['mobile', 'tablet', 'desktop'])).optional(),
  platforms: z.array(z.string()).optional(),
  browsers: z.array(z.string()).optional(),
  excludeDeviceTypes: z.array(z.enum(['mobile', 'tablet', 'desktop'])).optional(),
  excludePlatforms: z.array(z.string()).optional(),
  excludeBrowsers: z.array(z.string()).optional(),
})

const TimeRuleSchema = z.object({
  type: z.literal('time'),
  daysOfWeek: z.array(z.number().min(0).max(6)).optional(),
  startTime: z.string().regex(VALIDATION_PATTERNS.TIME).optional(),
  endTime: z.string().regex(VALIDATION_PATTERNS.TIME).optional(),
  timezone: z.string().regex(VALIDATION_PATTERNS.TIMEZONE).optional(),
  dateRange: z.object({
    start: z.date(),
    end: z.date(),
  }).optional(),
})

const ScheduleRuleSchema = z.object({
  type: z.literal('schedule'),
  scheduleStart: z.date(),
  scheduleEnd: z.date().optional(),
  timezone: z.string().regex(VALIDATION_PATTERNS.TIMEZONE),
  recurring: z.object({
    pattern: z.enum(['daily', 'weekly', 'monthly']),
    interval: z.number().min(1).max(365),
    endDate: z.date().optional(),
  }).optional(),
})

const RuleConfigurationSchema = z.union([
  ReferrerRuleSchema,
  LocationRuleSchema,
  DeviceRuleSchema,
  TimeRuleSchema,
  ScheduleRuleSchema,
])

/**
 * Rule validator class
 */
export class RuleValidator {
  /**
   * Validate a single rule configuration
   */
  public static validateRule(rule: RuleConfiguration): RuleValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    const suggestions: string[] = []

    try {
      // Schema validation
      RuleConfigurationSchema.parse(rule)
    } catch (error) {
      if (error instanceof z.ZodError) {
        errors.push(...error.errors.map(e => `${e.path.join('.')}: ${e.message}`))
      } else {
        errors.push('Unknown validation error')
      }
    }

    // Semantic validation
    const semanticResult = this.validateSemantics(rule)
    errors.push(...semanticResult.errors)
    warnings.push(...semanticResult.warnings)
    suggestions.push(...semanticResult.suggestions)

    // Performance validation
    const performanceResult = this.validatePerformance(rule)
    warnings.push(...performanceResult.warnings)
    suggestions.push(...performanceResult.suggestions)

    // Security validation
    const securityResult = this.validateSecurity(rule)
    errors.push(...securityResult.errors)
    warnings.push(...securityResult.warnings)

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      performanceImpact: this.assessPerformanceImpact(rule),
      suggestions,
    }
  }

  /**
   * Validate multiple conditions for a link
   */
  public static validateConditions(
    conditions: EnhancedLinkCondition[]
  ): RuleValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    const suggestions: string[] = []

    // Validate individual conditions
    for (const condition of conditions) {
      const result = this.validateRule(condition.rules)
      if (!result.isValid) {
        errors.push(`Condition ${condition.id}: ${result.errors.join(', ')}`)
      }
      warnings.push(...result.warnings.map(w => `Condition ${condition.id}: ${w}`))
      suggestions.push(...result.suggestions.map(s => `Condition ${condition.id}: ${s}`))
    }

    // Validate condition count
    if (conditions.length > PERFORMANCE_THRESHOLDS.MAX_CONDITIONS_PER_LINK) {
      errors.push(`Too many conditions: ${conditions.length}. Maximum allowed: ${PERFORMANCE_THRESHOLDS.MAX_CONDITIONS_PER_LINK}`)
    } else if (conditions.length > PERFORMANCE_THRESHOLDS.HIGH_CONDITION_COUNT_WARNING) {
      warnings.push(`High condition count: ${conditions.length}. Consider optimizing for better performance.`)
    }

    // Validate priority distribution
    const priorities = conditions.map(c => c.priority)
    const uniquePriorities = new Set(priorities)
    if (uniquePriorities.size < priorities.length) {
      warnings.push('Multiple conditions have the same priority. Consider adjusting priorities for deterministic evaluation.')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      performanceImpact: this.assessOverallPerformanceImpact(conditions),
      suggestions,
    }
  }

  /**
   * Validate semantic correctness of a rule
   */
  private static validateSemantics(rule: RuleConfiguration): {
    errors: string[]
    warnings: string[]
    suggestions: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []
    const suggestions: string[] = []

    switch (rule.type) {
      case 'referrer':
        this.validateReferrerSemantics(rule, errors, warnings, suggestions)
        break
      case 'location':
        this.validateLocationSemantics(rule, errors, warnings, suggestions)
        break
      case 'device':
        this.validateDeviceSemantics(rule, errors, warnings, suggestions)
        break
      case 'time':
        this.validateTimeSemantics(rule, errors, warnings, suggestions)
        break
      case 'schedule':
        this.validateScheduleSemantics(rule, errors, warnings, suggestions)
        break
    }

    return { errors, warnings, suggestions }
  }

  /**
   * Validate referrer rule semantics
   */
  private static validateReferrerSemantics(
    rule: ReferrerRule,
    errors: string[],
    warnings: string[],
    suggestions: string[]
  ): void {
    // Check for regex patterns when matchType is regex
    if (rule.matchType === 'regex') {
      for (const domain of rule.domains) {
        try {
          new RegExp(domain)
          if (domain.length > VALIDATION_CONFIG.MAX_REGEX_LENGTH) {
            warnings.push(`Regex pattern too long: ${domain.length} characters. Consider simplifying.`)
          }
        } catch (error) {
          errors.push(`Invalid regex pattern: ${domain}`)
        }
      }
    }

    // Check for overlapping domains
    if (rule.excludeDomains) {
      const overlap = rule.domains.filter(d => rule.excludeDomains!.includes(d))
      if (overlap.length > 0) {
        errors.push(`Domains appear in both include and exclude lists: ${overlap.join(', ')}`)
      }
    }

    // Performance suggestions
    if (rule.domains.length > 10) {
      suggestions.push('Consider using regex patterns for better performance with many domains')
    }
  }

  /**
   * Validate location rule semantics
   */
  private static validateLocationSemantics(
    rule: LocationRule,
    errors: string[],
    warnings: string[],
    suggestions: string[]
  ): void {
    // Check if at least one location criteria is specified
    const hasIncludeCriteria = !!(rule.countries?.length || rule.regions?.length || rule.cities?.length)
    const hasExcludeCriteria = !!(rule.excludeCountries?.length || rule.excludeRegions?.length || rule.excludeCities?.length)
    
    if (!hasIncludeCriteria && !hasExcludeCriteria) {
      errors.push('At least one location criteria must be specified')
    }

    // Check for overlapping criteria
    if (rule.countries && rule.excludeCountries) {
      const overlap = rule.countries.filter(c => rule.excludeCountries!.includes(c))
      if (overlap.length > 0) {
        errors.push(`Countries appear in both include and exclude lists: ${overlap.join(', ')}`)
      }
    }
  }

  /**
   * Validate device rule semantics
   */
  private static validateDeviceSemantics(
    rule: DeviceRule,
    errors: string[],
    warnings: string[],
    suggestions: string[]
  ): void {
    // Check if at least one device criteria is specified
    const hasIncludeCriteria = !!(rule.deviceTypes?.length || rule.platforms?.length || rule.browsers?.length)
    const hasExcludeCriteria = !!(rule.excludeDeviceTypes?.length || rule.excludePlatforms?.length || rule.excludeBrowsers?.length)
    
    if (!hasIncludeCriteria && !hasExcludeCriteria) {
      errors.push('At least one device criteria must be specified')
    }

    // Check for overlapping criteria
    if (rule.deviceTypes && rule.excludeDeviceTypes) {
      const overlap = rule.deviceTypes.filter(d => rule.excludeDeviceTypes!.includes(d))
      if (overlap.length > 0) {
        errors.push(`Device types appear in both include and exclude lists: ${overlap.join(', ')}`)
      }
    }
  }

  /**
   * Validate time rule semantics
   */
  private static validateTimeSemantics(
    rule: TimeRule,
    errors: string[],
    warnings: string[],
    suggestions: string[]
  ): void {
    // Check if at least one time criteria is specified
    const hasCriteria = !!(rule.daysOfWeek?.length || rule.startTime || rule.endTime || rule.dateRange)
    if (!hasCriteria) {
      errors.push('At least one time criteria must be specified')
    }

    // Validate time range
    if (rule.startTime && rule.endTime) {
      const start = this.parseTime(rule.startTime)
      const end = this.parseTime(rule.endTime)
      if (start >= end) {
        warnings.push('End time should be after start time. Consider if this spans midnight.')
      }
    }

    // Validate date range
    if (rule.dateRange) {
      if (rule.dateRange.start >= rule.dateRange.end) {
        errors.push('Date range end must be after start')
      }
      
      const daysDiff = Math.ceil((rule.dateRange.end.getTime() - rule.dateRange.start.getTime()) / (1000 * 60 * 60 * 24))
      if (daysDiff > 365) {
        warnings.push('Date range spans more than a year. Consider using recurring patterns.')
      }
    }
  }

  /**
   * Validate schedule rule semantics
   */
  private static validateScheduleSemantics(
    rule: ScheduleRule,
    errors: string[],
    warnings: string[],
    suggestions: string[]
  ): void {
    // Validate schedule dates
    if (rule.scheduleEnd && rule.scheduleStart >= rule.scheduleEnd) {
      errors.push('Schedule end must be after start')
    }

    // Validate recurring pattern
    if (rule.recurring) {
      if (rule.recurring.endDate && rule.recurring.endDate <= rule.scheduleStart) {
        errors.push('Recurring end date must be after schedule start')
      }
      
      if (rule.recurring.interval > 30 && rule.recurring.pattern === 'daily') {
        warnings.push('Daily recurring interval is very high. Consider using weekly or monthly pattern.')
      }
    }
  }

  /**
   * Validate performance characteristics of a rule
   */
  private static validatePerformance(rule: RuleConfiguration): {
    warnings: string[]
    suggestions: string[]
  } {
    const warnings: string[] = []
    const suggestions: string[] = []

    const complexity = this.calculateComplexity(rule)
    if (complexity > VALIDATION_CONFIG.COMPLEXITY_WARNING_THRESHOLD) {
      warnings.push(`High complexity score: ${complexity}. This may impact performance.`)
      suggestions.push('Consider simplifying the rule or splitting into multiple conditions')
    }

    return { warnings, suggestions }
  }

  /**
   * Validate security aspects of a rule
   */
  private static validateSecurity(rule: RuleConfiguration): {
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    // Check for potentially dangerous regex patterns
    if (rule.type === 'referrer' && rule.matchType === 'regex') {
      for (const domain of rule.domains) {
        if (this.isDangerousRegex(domain)) {
          warnings.push(`Potentially dangerous regex pattern detected: ${domain}`)
        }
      }
    }

    return { errors, warnings }
  }

  /**
   * Assess performance impact of a rule
   */
  private static assessPerformanceImpact(rule: RuleConfiguration): 'low' | 'medium' | 'high' {
    const complexity = this.calculateComplexity(rule)
    
    if (complexity < 10) return 'low'
    if (complexity < 30) return 'medium'
    return 'high'
  }

  /**
   * Assess overall performance impact of multiple conditions
   */
  private static assessOverallPerformanceImpact(
    conditions: EnhancedLinkCondition[]
  ): 'low' | 'medium' | 'high' {
    const totalComplexity = conditions.reduce((sum, condition) => {
      return sum + this.calculateComplexity(condition.rules)
    }, 0)

    if (totalComplexity < 20) return 'low'
    if (totalComplexity < 60) return 'medium'
    return 'high'
  }

  /**
   * Calculate complexity score for a rule
   */
  private static calculateComplexity(rule: RuleConfiguration): number {
    let complexity = 0

    switch (rule.type) {
      case 'referrer':
        complexity += rule.domains.length * 2
        if (rule.matchType === 'regex') complexity += 10
        if (rule.excludeDomains) complexity += rule.excludeDomains.length
        break
      case 'location':
        complexity += (rule.countries?.length || 0) * 1
        complexity += (rule.regions?.length || 0) * 2
        complexity += (rule.cities?.length || 0) * 3
        break
      case 'device':
        complexity += (rule.deviceTypes?.length || 0) * 1
        complexity += (rule.platforms?.length || 0) * 2
        complexity += (rule.browsers?.length || 0) * 2
        break
      case 'time':
        complexity += (rule.daysOfWeek?.length || 0) * 1
        if (rule.startTime || rule.endTime) complexity += 3
        if (rule.dateRange) complexity += 5
        break
      case 'schedule':
        complexity += 2
        if (rule.recurring) complexity += 5
        break
    }

    return complexity
  }

  /**
   * Parse time string to minutes since midnight
   */
  private static parseTime(timeStr: string): number {
    const [hours, minutes] = timeStr.split(':').map(Number)
    return hours * 60 + minutes
  }

  /**
   * Check if regex pattern is potentially dangerous
   */
  private static isDangerousRegex(pattern: string): boolean {
    // Check for catastrophic backtracking patterns
    const dangerousPatterns = [
      /\(\.\*\)\+/,  // (.*)+
      /\(\.\+\)\*/,  // (.+)*
      /\(\w\*\)\+/,  // (\w*)+
      /\(\w\+\)\*/,  // (\w+)*
    ]

    return dangerousPatterns.some(dangerous => dangerous.test(pattern))
  }
}
