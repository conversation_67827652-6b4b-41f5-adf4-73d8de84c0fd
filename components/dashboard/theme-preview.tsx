"use client"

import { Card, CardContent } from '@/components/ui/card'
import { ExternalLink, Instagram, Twitter, Github } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import type { ProfileTheme } from '@/lib/types'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

interface ThemePreviewProps {
  theme: ProfileTheme
  backgroundType: 'color' | 'gradient' | 'image'
  backgroundValue: string
  className?: string
  user?: {
    displayName: string
    bio?: string | null
    profileImage?: string | null
  }
}

export function ThemePreview({ 
  theme, 
  backgroundType, 
  backgroundValue, 
  className,
  user = {
    displayName: 'Your Name',
    bio: 'Your bio goes here. Tell people about yourself!',
    profileImage: null
  }
}: ThemePreviewProps) {
  const getBackgroundStyle = () => {
    switch (backgroundType) {
      case 'color':
        return { backgroundColor: backgroundValue }
      case 'gradient':
        return { background: backgroundValue }
      case 'image':
        return { 
          backgroundImage: `url(${backgroundValue})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }
      default:
        return { backgroundColor: theme.backgroundColor }
    }
  }

  const sampleLinks = [
    { title: 'Instagram', icon: Instagram, url: '#' },
    { title: 'Twitter', icon: Twitter, url: '#' },
    { title: 'GitHub', icon: Github, url: '#' },
    { title: 'Website', icon: ExternalLink, url: '#' },
  ]

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardContent className="p-0">
        <div 
          className="min-h-[400px] p-8 flex flex-col items-center justify-center text-center relative"
          style={{
            ...getBackgroundStyle(),
            fontFamily: theme.fontFamily,
            color: theme.textColor
          }}
        >
          {/* Overlay for better text readability on images */}
          {backgroundType === 'image' && (
            <div className="absolute inset-0 bg-black/20" />
          )}
          
          <div className="relative z-10 w-full max-w-sm">
            {/* Profile Image */}
            <Avatar className="w-20 h-20 mx-auto mb-4 border-2" style={{ borderColor: theme.primaryColor }}>
              <AvatarImage src={user.profileImage || undefined} />
              <AvatarFallback style={{ backgroundColor: theme.primaryColor, color: theme.backgroundColor }}>
                {user.displayName.split(' ').map(n => n[0]).join('').toUpperCase()}
              </AvatarFallback>
            </Avatar>

            {/* Name */}
            <h1 className="text-2xl font-bold mb-2" style={{ color: theme.textColor }}>
              {user.displayName}
            </h1>

            {/* Bio */}
            {user.bio && (
              <p className="text-sm mb-6 opacity-80" style={{ color: theme.textColor }}>
                {user.bio}
              </p>
            )}

            {/* Sample Links */}
            <div className="space-y-3 w-full">
              {sampleLinks.map((link, index) => {
                const IconComponent = link.icon
                return (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 rounded-lg transition-all hover:scale-105 cursor-pointer"
                    style={{ 
                      backgroundColor: theme.primaryColor,
                      color: theme.backgroundColor
                    }}
                  >
                    <div className="flex items-center space-x-3">
                      <IconComponent className="h-5 w-5" />
                      <span className="font-medium">{link.title}</span>
                    </div>
                    <ExternalLink className="h-4 w-4 opacity-70" />
                  </div>
                )
              })}
            </div>

            {/* Theme Badge */}
            <div className="mt-6">
              <Badge 
                variant="secondary" 
                className="text-xs"
                style={{ 
                  backgroundColor: theme.secondaryColor,
                  color: theme.backgroundColor
                }}
              >
                {theme.preset ? `${theme.preset} theme` : 'Custom theme'}
              </Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}